import java.io.*;
import java.util.*;

public class Main {
    public static void main(String[] args) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(System.in));
        Map<Integer, List<RekordData>> recordsYearly = new TreeMap<>();
        while (true) {
            String inputLine = reader.readLine();
            if (inputLine == null || inputLine.isEmpty()) break;
            String[] parts = inputLine.split("\\s+");
            if (parts.length < 3) continue;
            String date = parts[0];
            if (date.length() < 4) continue;

            int yyyy;
            try {
                yyyy = Integer.parseInt(date.substring(0, 4));
            } catch (NumberFormatException e) {
                continue;
            }
            double depth;
            try {
                depth = Double.parseDouble(parts[parts.length - 1]);
            } catch (NumberFormatException e) {
                continue;
            }
            StringBuilder sb = new StringBuilder();
            for (int i = 1; i < parts.length - 1; i++) {
                sb.append(parts[i]);
                if (i < parts.length - 2) sb.append(" ");
            }
            String place = sb.toString();

            recordsYearly.putIfAbsent(yyyy, new ArrayList<>());
            recordsYearly.get(yyyy).add(new RekordData(yyyy, place, depth));
        }
        reader.close();

        StringBuilder output = new StringBuilder();
        for (int y : recordsYearly.keySet()) {
            List<RekordData> dataList = recordsYearly.get(y);
            Collections.sort(dataList);
            output.append(y).append("\n");
            Set<String> shownPlaces = new HashSet<>();
            int printedSoFar = 0;
            for (RekordData row : dataList) {
                if (!shownPlaces.contains(row.getLocation())) {
                    if (printedSoFar < 5) {
                        output.append(row.getLocation())
                                .append(" ")
                                .append(row.getSnowDepth())
                                .append("\n");
                        shownPlaces.add(row.getLocation());
                        printedSoFar++;
                    }
                }
            }
        }
        System.out.print(output.toString().trim());
    }
}

class RekordData implements Comparable<RekordData> {
    private final int year;
    private final String location;
    private final double snowDepth;

    public RekordData(int year, String location, double snowDepth) {
        this.year = year;
        this.location = location;
        this.snowDepth = snowDepth;
    }

    public int getYear() {
        return year;
    }

    public String getLocation() {
        return location;
    }

    public double getSnowDepth() {
        return snowDepth;
    }

    @Override
    public int compareTo(RekordData other) {
        int depthComparison = Double.compare(other.snowDepth, this.snowDepth);
        if (depthComparison != 0) {
            return depthComparison;
        }
        return this.location.compareTo(other.location);
    }
}