# Comprehensive edge case test for Problem 6
# Testing various scenarios that might cause "Wrong answer" in <PERSON><PERSON> test case 4

# Test 1: Precision issues with very close floating point values
20201201 Stockholm 1.11111111
20201202 Stockholm 1.11111112
20201203 Göteborg 1.11111113

# Test 2: Multiple measurements same location same year - keep highest
20211201 Uppsala 2.5
20211215 Uppsala 2.7
20211230 Uppsala 2.3

# Test 3: Locations with special characters and spaces
20221201 New York City 1.5
20221202 Los Angeles County 1.3
20221203 San Francisco Bay Area 1.7
20221204 Åre 2.1
20221205 Göteborg 1.9

# Test 4: Edge case with exactly 5 locations (boundary test)
20231201 Alpha 5.0
20231202 Beta 4.0
20231203 Gamma 3.0
20231204 Delta 2.0
20231205 Epsilon 1.0

# Test 5: More than 5 locations - test top 5 selection
20241201 Location1 10.0
20241202 Location2 9.0
20241203 Location3 8.0
20241204 Location4 7.0
20241205 Location5 6.0
20241206 Location6 5.0
20241207 Location7 4.0

# Test 6: Same depth values - alphabetical sorting
20251201 Zebra 2.5
20251202 Alpha 2.5
20251203 Beta 2.5
20251204 Charlie 2.5
20251205 Delta 2.5
20251206 Echo 2.5

# Test 7: Very small values close to zero
20261201 Place1 0.01
20261202 Place2 0.02
20261203 Place3 0.03

# Test 8: Large values
20271201 Mountain1 99.9
20271202 Mountain2 99.8
20271203 Mountain3 99.7

# Test 9: Mixed precision values
20281201 Test1 1.0
20281202 Test2 1.00
20281203 Test3 1.000
20281204 Test4 0.9999999
20281205 Test5 1.0000001
