import java.time.LocalDate; // Importerar LocalDate för att hantera datum.
import java.time.format.DateTimeFormatter; // Importerar DateTimeFormatter för att parsa datumsträngar.
import java.util.Objects; // Importerar Objects för att använda hash- och equals-metoder.

// Klass som representerar en snödjupsmätning och är jämförbar för sortering.
public class SnowDepthData implements Comparable<SnowDepthData> {
    private final LocalDate date; // Datum för mätningen.
    private final String location; // Platsen för mätningen.
    private final double depth; // Snödjupet i meter.
    private final int year; // Året för mätningen.

    // Konstruktor för att skapa ett SnowDepthData-objekt.
    public SnowDepthData(String dateString, String location, double depth) {
        // Parsar datumsträngen (format: YYYYMMDD) till ett LocalDate-objekt.
        this.date = LocalDate.parse(dateString, DateTimeFormatter.ofPattern("yyyyMMdd"));
        this.location = location; // Lagrar platsnamnet.
        this.depth = depth; // Lagrar snödjupet.
        this.year = this.date.getYear(); // Extraherar och lagrar året från datumet.
    }

    // Returnerar året för mätningen.
    public int getYear() {
        return year;
    }

    // Returnerar platsen för mätningen.
    public String getLocation() {
        return location;
    }

    // Returnerar snödjupet för mätningen.
    public double getDepth() {
        return depth;
    }

    // Definierar sorteringsordningen: djup fallande, sedan plats alfabetiskt stigande.
    @Override
    public int compareTo(SnowDepthData other) {
        // Jämför snödjup i fallande ordning (högst djup först).
        int depthComparison = Double.compare(other.depth, this.depth);
        if (depthComparison != 0) {
            return depthComparison; // Returnera jämförelsen om djupen är olika.
        }
        // Om djupen är lika, sortera alfabetiskt efter platsnamn (stigande ordning).
        return this.location.compareTo(other.location);
    }

    // Definierar när två SnowDepthData-objekt är lika (samma år och plats).
    @Override
    public boolean equals(Object o) {
        if (this == o) return true; // Samma objektreferens.
        if (o == null || getClass() != o.getClass()) return false; // Null eller olika klass.
        SnowDepthData that = (SnowDepthData) o; // Casta till SnowDepthData.
        // Två objekt är lika om de har samma år och plats.
        return year == that.year &&
               Objects.equals(location, that.location);
    }

    // Genererar hashkod baserat på plats och år.
    @Override
    public int hashCode() {
        // Använder Objects.hash för att skapa en hashkod från location och year.
        return Objects.hash(location, year);
    }

    // Returnerar en strängrepresentation av objektet (t.ex. "Kiruna 1.2").
    @Override
    public String toString() {
        // Formaterar utskriften med platsnamn och snödjup.
        return location + " " + depth;
    }
}


