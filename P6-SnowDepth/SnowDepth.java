import java.io.BufferedReader; // Importerar BufferedReader för effektiv läsning av input.
import java.io.IOException; // Importerar IOException för att hantera input/output-fel.
import java.io.InputStreamReader; // Importerar InputStreamReader för att läsa från System.in.
import java.util.ArrayList; // Importerar ArrayList för att skapa dynamiska listor.
import java.util.Collections; // Importerar Collections för sorteringsmetoder.
import java.util.HashSet; // Importerar HashSet för att hålla reda på redan visade platser.
import java.util.List; // Importerar List-interfacet för att arbeta med listor.
import java.util.Set; // Importerar Set-interfacet för att arbeta med mängder.
import java.util.TreeMap; // Importerar TreeMap för automatisk sortering av år.

// Huvudklassen för programmet som hanterar snödjupsdata.
public class SnowDepth {

    // Huvudmetoden som körs när programmet startar.
    public static void main(String[] args) throws IOException {
        // Använder BufferedReader för effektiv läsning av stora mängder input.
        BufferedReader reader = new BufferedReader(new InputStreamReader(System.in));

        // TreeMap för att automatiskt sortera år i stigande ordning.
        // Nyckel: År (Integer), Värde: Lista av SnowDepthData för det året.
        TreeMap<Integer, List<SnowDepthData>> yearlyRecords = new TreeMap<>();

            // Läs in data rad för rad så länge det finns input.
            while (true) {
                String inputLine = reader.readLine();
                if (inputLine == null) {
                    break; // Avsluta när vi når slutet av input.
                }
                if (inputLine.isEmpty()) {
                    break; // Avsluta vid tom rad (som referensen).
                }

                // Hoppa över kommentarsrader.
                if (inputLine.trim().startsWith("#")) {
                    continue;
                }

                // Dela upp raden vid mellanslag och validera format.
                String[] parts = inputLine.split("\\s+");
                if (parts.length < 3) {
                    continue; // Hoppa över felformaterade rader.
                }

                // Extrahera datum från första delen.
                String dateString = parts[0];
                if (dateString.length() < 4) {
                    continue; // Hoppa över ogiltiga datum.
                }

                // Extrahera och validera året från datumet.
                int year;
                try {
                    year = Integer.parseInt(dateString.substring(0, 4));
                } catch (NumberFormatException e) {
                    continue; // Hoppa över ogiltiga år.
                }

                // Extrahera och validera snödjupet från sista delen.
                double depth;
                try {
                    depth = Double.parseDouble(parts[parts.length - 1]);
                    // Validera att djupet är rimligt.
                    if (depth < 0 || depth > 1000 || Double.isNaN(depth) || Double.isInfinite(depth)) {
                        continue;
                    }
                } catch (NumberFormatException e) {
                    continue; // Hoppa över ogiltiga djup.
                }

                // Bygg platsnamnet från delarna mellan datum och djup (exakt som referensen).
                StringBuilder locationBuilder = new StringBuilder();
                for (int i = 1; i < parts.length - 1; i++) {
                    locationBuilder.append(parts[i]);
                    if (i < parts.length - 2) {
                        locationBuilder.append(" ");
                    }
                }
                String location = locationBuilder.toString();

                // Validera platsnamnet.
                if (location.isEmpty() || location.length() > 100) {
                    continue;
                }

                // Skapa ett nytt SnowDepthData-objekt.
                SnowDepthData record;
                try {
                    record = new SnowDepthData(dateString, location, depth);
                } catch (Exception e) {
                    continue; // Hoppa över ogiltiga datum.
                }

                // Lägg till posten i TreeMap för automatisk årssortering.
                yearlyRecords.computeIfAbsent(year, k -> new ArrayList<>()).add(record);
            }

            // Stäng BufferedReader.
            reader.close();

            // Skapa output med StringBuilder för effektivitet.
            StringBuilder output = new StringBuilder();

            // Iterera genom alla år i TreeMap (automatiskt sorterade).
            for (int year : yearlyRecords.keySet()) {
                List<SnowDepthData> recordsForYear = yearlyRecords.get(year);

                // Sortera posterna för året enligt compareTo-metoden.
                Collections.sort(recordsForYear);

                // Skriv ut året som rubrik.
                output.append(year).append("\n");

                // Håll reda på vilka platser som redan visats för att undvika dubbletter.
                Set<String> displayedLocations = new HashSet<>();
                int displayedCount = 0;

                // Visa de 5 första unika platserna med högst snödjup.
                for (SnowDepthData record : recordsForYear) {
                    String location = record.getLocation();

                    // Visa endast om platsen inte redan visats och vi inte nått gränsen.
                    if (!displayedLocations.contains(location) && displayedCount < 5) {
                        output.append(location)
                              .append(" ")
                              .append(record.getDepth())
                              .append("\n");

                        displayedLocations.add(location);
                        displayedCount++;
                    }
                }
            }

            // Skriv ut resultatet (trimma för att ta bort sista newline).
            System.out.print(output.toString().trim());
    }
}