Problem G
Snödjup

Givet en lista med inrapporterade snödjup från olika platser ska du sammanställa en en rekordlista över de platser som haft de djupaste snötäckena olika år. Din resultatlista ska för varje år som det finns mätvärden först ange året, och sedan lista de fem platser som hade störst snödjup det året i fallande ordning. Om två platser hade samma snödjup så ska de listas i alfabetisk ordning efter ortnamnet. Se exempel nedan för formatering av utdata.

Du måste använda dig av en klass som kan lagra snödjupsdata, och som implementerar gränsnittet Comparable.
Indata

En lista där varje rad innehåller en observation på formatet: <datum> <ortnamn> <snödjup i meter>. Datumet står på formen YYYYMMDD, ortnamn kan vara ett eller flera ord, snödjupet är ett decimaltal, som använder “.” (en punkt) som decimaltecken. Indata är som mest 1 000 000 rader.
Utdata

Utdata: en sorterad lista där det för varje år som det finns minst en observation ska listas (radbrutet): <ortsnamn> <snödjup i meter>. Högst fem orter per år ska listas. Varje års lista ska inledas med årtalet på en ensam rad (se exempel nedan).

* Sample Input 1:
"20240216 Trysil 2.1
20240215 Kiruna 1.1
20230215 Kiruna 1.2
20240109 Abisko 1.6"

* Sample Output 1:
"2023
Kiruna 1.2
2024
Trysil 2.1
Abisko 1.6
Kiruna 1.1"

Sample Input 2: 
"19720214 Aspen 1.8
19730116 Aspen 1.9
19721223 Kiruna 1.2
19730117 Aspen 2.1
19721228 Kiruna 1.2
19721113 Boden 1.2
19720322 Chamonix 0.4
19720104 Val d'Isere 1.4
19721230 Kalmar 0.1
19721109 Leksand 0.3
19721121 Trysil 0.9
19721230 Kalmar 0.3
19721219 Leksand 0.1
19721124 Trysil 0.3"

Sample output 2: 
"1972
Aspen 1.8
Val d'Isere 1.4
Boden 1.2
Kiruna 1.2
Trysil 0.9
1973
Aspen 2.1"
