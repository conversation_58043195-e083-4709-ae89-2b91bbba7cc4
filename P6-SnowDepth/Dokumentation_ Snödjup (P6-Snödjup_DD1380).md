# Dokumentation: Sn<PERSON>dju<PERSON> (P6-Snödjup_DD1380)

Denna dokumentation beskriver implementeringen av lösningen till problemet "Snödjup". Målet är att du ska kunna förstå kodens funktionalitet och logik för att på ett övertygande sätt presentera den.

## Problembeskrivning

Problemet går ut på att, från en lista med snödjupsmätningar från olika platser och datum, sammanställa en rekordlista. För varje år ska de fem platser som haft störst snödjup det året listas i fallande ordning. Om två platser hade samma snödjup ska de listas i alfabetisk ordning efter ortnamnet.

**Indata:** En lista med rader, där varje rad innehåller `<datum> <ortnamn> <snödjup i meter>`. Datumet är på formen YYYYMMDD, ortnamnet kan vara ett eller flera ord, och snödjupet är ett decimaltal med punkt som decimaltecken.

**Utdata:** <PERSON><PERSON><PERSON> varje å<PERSON>, först året på en egen rad, sedan en lista med högst fem orter med störst snödjup för det året. Formatet för varje ort är `<ortsnamn> <snödjup i meter>` (med en decimal).

## Lösningens Översikt

Lösningen är uppdelad i två huvudklasser:

1.  `SnowDepthData`: En hjälpklass som representerar en enskild snödjupsmätning. Den lagrar datum, plats, snödjup och år. Den är också `Comparable`, vilket betyder att den vet hur den ska jämföras med andra `SnowDepthData`-objekt (för sortering).
2.  `SnowDepth`: Huvudklassen som läser in data, bearbetar den och skriver ut resultatet. Den använder en `TreeMap` för att automatiskt sortera år och säkerställer att endast unika platser per år visas i utskriften.

## Funktionsspecifikationer

### Klass: `SnowDepthData`

Denna klass kapslar in informationen för en enskild snödjupsmätning och definierar hur dessa mätningar ska jämföras och hanteras.

*   **Fält:**
    *   `private LocalDate date`: Datumet för mätningen.
    *   `private String location`: Platsen där mätningen gjordes.
    *   `private double depth`: Snödjupet i meter.
    *   `private int year`: Året för mätningen, utdraget från `date`.

*   **Konstruktor:**
    *   `public SnowDepthData(String dateString, String location, double depth)`
        *   **Beskrivning:** Skapar ett nytt `SnowDepthData`-objekt. Tolkar `dateString` till ett `LocalDate`-objekt och extraherar året.
        *   **Parametrar:**
            *   `dateString`: Datumet som en sträng (YYYYMMDD).
            *   `location`: Platsens namn.
            *   `depth`: Snödjupet som ett decimaltal.

*   **Metoder:**
    *   `public int getYear()`
        *   **Beskrivning:** Returnerar året för mätningen.
    *   `public String getLocation()`
        *   **Beskrivning:** Returnerar platsnamnet för mätningen.
    *   `public double getDepth()`
        *   **Beskrivning:** Returnerar snödjupet för mätningen.
    *   `@Override public int compareTo(SnowDepthData other)`
        *   **Beskrivning:** Definierar sorteringsordningen för `SnowDepthData`-objekt. Sorterar först efter `depth` i fallande ordning (störst djup först). Om djupen är lika, sorterar den efter `location` i alfabetisk ordning (stigande).
    *   `@Override public boolean equals(Object o)`
        *   **Beskrivning:** Definierar när två `SnowDepthData`-objekt anses vara lika. Två objekt är lika om de har samma `year` och `location`. Detta är viktigt för att `HashMap` ska kunna hantera unika platser per år korrekt.
    *   `@Override public int hashCode()`
        *   **Beskrivning:** Genererar en hashkod för objektet, baserat på `location` och `year`. Nödvändig för att `equals` ska fungera korrekt med `HashMap`.
    *   `@Override public String toString()`
        *   **Beskrivning:** Returnerar en strängrepresentation av objektet i formatet "Plats Djup" (t.ex. "Kiruna 1.2"), med snödjupet formaterat till en decimal.

### Klass: `SnowDepth`

Detta är huvudprogrammet som hanterar inläsning, bearbetning och utskrift av snödjupsdata.

*   **Metoder:**
    *   `public static void main(String[] args)`
        *   **Beskrivning:** Huvudmetoden där programmet startar. Den läser in data, organiserar den, sorterar den och skriver ut resultatet enligt problembeskrivningen.

## Kodgenomgång (Workflow)

Lösningen följer ett tydligt arbetsflöde för att hantera snödjupsdata:

### 1. Inläsning och Parsning av Data

Programmet börjar med att läsa in indata rad för rad med hjälp av en `BufferedReader` för effektiv läsning. Varje rad representerar en snödjupsmätning. Raden delas upp i tre huvuddelar: datum, plats och snödjup.

*   **Robust parsning:** Programmet hanterar felformaterade rader, kommentarer (rader som börjar med #), och tomma rader genom att hoppa över dem.
*   **Validering:** Datum, platsnamn och snödjup valideras för att säkerställa att de är rimliga värden.
*   **Hantering av platsnamn med flera ord:** En viktig detalj är att platsnamnet kan bestå av flera ord (t.ex. "Val d'Isere"). Koden hanterar detta genom att dela upp raden i delar baserat på mellanslag och sedan bygga ihop platsnamnet från de delar som ligger mellan datumet (första delen) och snödjupet (sista delen).
*   **Skapande av `SnowDepthData`-objekt:** För varje giltig inläst rad skapas ett `SnowDepthData`-objekt. Detta objekt kapslar in all relevant information för den specifika mätningen.

### 2. Lagring och Organisering av Data

All inläst data lagras i en `TreeMap<Integer, List<SnowDepthData>> yearlyRecords`.

*   **TreeMap för automatisk sortering:** `TreeMap` används för att automatiskt sortera åren i stigande ordning. Nyckel är året (`Integer`) och värdet är en lista av `SnowDepthData`-objekt för det året.
*   **Enkel datastruktur:** Varje giltig mätning läggs till i listan för motsvarande år med `computeIfAbsent` metoden, som skapar en ny lista om det är första mätningen för det året.
*   **Ingen förhandsaggregering:** Till skillnad från tidigare implementationer aggregeras inte data per plats i förväg, utan all sortering och filtrering sker vid utskrift.

### 3. Bearbetning och Utskrift av Resultat

När all data har lästs in, bearbetas och skrivs resultatet ut:

*   **Automatisk årssortering:** Eftersom `TreeMap` används är åren redan sorterade i stigande ordning (kronologiskt).
*   **Iteration över år:** Programmet loopar igenom varje år i `TreeMap`.
*   **Sortering av data per år:** För varje år hämtas alla `SnowDepthData`-objekt och sorteras enligt `compareTo`-metoden i `SnowDepthData`-klassen (snödjup fallande, sedan platsnamn alfabetiskt).
*   **Unika platser och topp 5:** Programmet använder en `HashSet<String>` för att hålla reda på vilka platser som redan visats för det aktuella året. Endast de första 5 unika platserna med högst snödjup visas.
*   **Formaterad utskrift:** Varje post formateras som "Plats Djup" där djupet visas som ett decimaltal, precis som specificerat i problembeskrivningen.
*   **Effektiv strängbyggning:** `StringBuilder` används för att bygga upp utskriften effektivt innan den skrivs ut.

Detta arbetsflöde säkerställer att alla krav i problembeskrivningen uppfylls, från robust inläsning och validering till sortering och formaterad utskrift av resultatet.

## Aktuell Status

**Testresultat:** Programmet klarar för närvarande 3 av 7 testfall på Kattis. Testfall 4 misslyckas med "Wrong answer".

**Implementerade funktioner:**
- Robust parsning med validering av indata
- Hantering av kommentarer och felformaterade rader
- Automatisk årssortering med TreeMap
- Korrekt sortering enligt snödjup (fallande) och platsnamn (alfabetiskt)
- Unika platser per år (endast högsta värdet för varje plats visas)
- Effektiv minneshantering med BufferedReader och StringBuilder

**Kända problem:**
- Testfall 4 ger fel svar, troligen relaterat till specifika edge cases i dataformatet eller sorteringslogiken
- Ytterligare felsökning behövs för att identifiera exakt vilka testfall som misslyckas

