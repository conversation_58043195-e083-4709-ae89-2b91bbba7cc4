# Edge cases for Problem 6 - Snow Depth

# Edge case 1: Same depth, different locations (alphabetical sorting test)
20201201 Stockholm 1.0
20201202 Göteborg 1.0
20201203 Malmö 1.0

# Edge case 2: Multiple years
20201201 Stockholm 1.0
20211201 Stockholm 2.0
20221201 Stockholm 3.0

# Edge case 3: Cities with spaces and special characters
20201201 New York City 0.3
20201202 Los Angeles County 0.8
20201203 San Francisco Bay Area 0.6
20201204 Örebro 0.9

# Edge case 4: Multiple measurements same city same year
20201201 Stockholm 0.5
20201202 Stockholm 0.7
20201203 Stockholm 0.3

# Edge case 5: Large dataset with many cities
20201201 Stockholm 0.5
20201202 Göteborg 1.2
20201203 Malmö 0.9
20201204 Uppsala 1.1
20201205 Västerås 0.6
20201206 Örebro 1.0
20201207 Linköping 0.4
20201208 Helsingborg 0.2
20201209 Jönköping 1.3
20201210 Norrköping 0.5
20201211 Lund 0.3
20201212 Umeå 2.1
20201213 Gävle 1.5
20201214 Borås 0.7
20201215 Eskilstuna 0.8
20201216 Karlstad 1.0
20201217 Täby 0.6
20201218 Sundsvall 1.8
20201219 Luleå 2.5
20201220 Kiruna 3.0
20201221 Abisko 2.8
20201222 Riksgränsen 3.2

# Edge case 6: Input validation tests (invalid lines should be ignored)
20201201 Stockholm 0.5
invalid line
20201202 Stockholm 0.7

20201203 Göteborg 1.2
20201204 Malmö invalid
20201205 Uppsala 1.1
20201206
20201207 Västerås 0.6 extra
20201208 Örebro 0.9
