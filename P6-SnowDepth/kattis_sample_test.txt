# Exact test case based on <PERSON><PERSON> sample from images
# Expected output should match the images exactly

# From the images, the expected output shows:
# 1903: Luleå 2.8
# 1907: Nuuq 1.4  
# 1973: Nagano 3.0
# 2024: Nagano 2.4, Aspen 2.3, <PERSON><PERSON><PERSON> 2.2, Bergen 1.7
# 2023: Aspen 0.3, <PERSON><PERSON> 0.3, Chamonix 0.3, <PERSON><PERSON><PERSON> 0.3, <PERSON><PERSON><PERSON> 0.3

19030527 Luleå 2.76
19070703 Nuuq 1.35
19730407 Nagano 2.96

20240920 Nagano 2.36
20240921 Vallois 2.10
20240922 Vallois 2.17
20240923 Aspen 1.33
20240924 Bergen 1.71
20240925 Aspen 2.33

20230214 Aspen 0.3
20231223 Kiruna 0.3
20231113 Boden 0.3
20230322 Chamonix 0.3
20230104 Val d'Isere 0.3
20231230 Kalmar 0.3
