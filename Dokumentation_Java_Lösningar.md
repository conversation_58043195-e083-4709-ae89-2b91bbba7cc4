# Dokumentation - Java Programmeringslösningar

## Översikt
Detta dokument beskriver fyra Java-program som löser olika algoritmiska problem. Varje program demonstrerar olika programmeringstekniker och datastrukturer.

## Program 1: LetterCounter.java

### Syfte
Räknar frekvensen av bokstäver (A-Z) i en given textinmatning och presenterar resultatet i alfabetisk ordning.

### Funktionalitet
- Läser en textrad från användaren
- Filtrerar bort icke-alfabetiska tecken
- Konverterar alla bokstäver till versaler för enhetlig behandling
- Räknar förekomsten av varje bokstav
- Presenterar resultatet i formatet "BOKSTAV:ANTAL"

### Algoritm
1. Initialisera en HashMap med alla bokstäver A-Z satta till frekvens 0
2. L<PERSON>s inmatningstext och filtrera med regex `[^a-zA-Z]`
3. Iterera genom varje tecken och öka motsvarande räknare
4. Bygg resultatsträngen med endast bokstäver som har frekvens > 0
5. Skriv ut resultatet eller "0" om inga bokstäver hittades

### Datastrukturer
- `HashMap<Character, Integer>`: Lagrar bokstavsfrekvenser
- `StringBuilder`: Effektiv strängbyggning för utdata

### Komplexitet
- Tidskomplexitet: O(n) där n är längden på inmatningstexten
- Rymdkomplexitet: O(1) (konstant antal bokstäver i alfabetet)

### Exempel
```
Input: "Hello World! 123"
Output: "D:1 E:1 H:1 L:3 O:2 R:1 W:1"
```

## Program 2: RectanglePerimeter.java

### Syfte
Beräknar den minsta möjliga omkretsen för en rektangel med heltalssidor som har en given area.

### Funktionalitet
- Tar en area A som indata
- Hittar alla möjliga rektangeldimensioner med heltalssidor
- Beräknar omkretsen för varje möjlig rektangel
- Returnerar den minsta omkretsen

### Algoritm
1. Iterera genom alla möjliga längder från 1 till √area
2. För varje längd som är en divisor av area, beräkna motsvarande bredd
3. Beräkna omkretsen: P = 2 × (längd + bredd)
4. Håll reda på den minsta omkretsen

### Matematisk grund
- För en given area A = längd × bredd
- Omkrets P = 2 × (längd + bredd)
- För att minimera P måste längd och bredd vara så nära varandra som möjligt
- Detta uppnås när rektangeln närmar sig kvadratform

### Optimering
- Endast kontrollera divisorer upp till √area för effektivitet
- Varje divisor d ger automatiskt motsvarande divisor area/d

### Komplexitet
- Tidskomplexitet: O(√n) där n är arean
- Rymdkomplexitet: O(1)

### Exempel
```
Input: 12
Möjliga rektanglar: 1×12 (P=26), 2×6 (P=16), 3×4 (P=14)
Output: 14
```

## Program 3: Labb3.java

### Syfte
Rekonstruerar en ursprunglig textmening från komprimerad data där varje ord är kopplat till sina positioner i texten.

### Funktionalitet
- Parsar indata i formatet "ord1:pos1 pos2;ord2:pos3 pos4;..."
- Rekonstruerar den ursprungliga texten genom att placera ord på angivna positioner
- Hanterar UTF-8-kodning för internationella tecken
- Använder funktionell programmering med Java Streams

### Algoritm
1. Dela upp indata vid semikolon för att få ord:position-par
2. För varje par, extrahera ordet och dess positionslista
3. Lagra i en LinkedHashMap för att bevara ordning
4. Bygg resultatsträngen med StringBuilder
5. För varje ord, placera det på alla dess angivna positioner
6. Ta bort avslutande mellanslag från resultatet

### Datastrukturer
- `LinkedHashMap<String, List<Integer>>`: Bevarar ordning på inmatade ord
- `StringBuilder`: Dynamisk strängkonstruktion
- Java Streams: Funktionell databehandling

### Tekniker
- **Funktionell programmering**: Använder streams, map, filter, forEach
- **UTF-8-hantering**: Scanner med StandardCharsets.UTF_8
- **Dynamisk storlekshantering**: StringBuilder utökas automatiskt

### Komplexitet
- Tidskomplexitet: O(n × m) där n är antal ord och m är genomsnittlig ordlängd
- Rymdkomplexitet: O(k) där k är längden på den rekonstruerade texten

### Exempel
```
Input: "Hej:0;världen:4"
Output: "Hej världen"
```

## Program 4: Factoradic.java

### Syfte
Konverterar mellan det faktoriella talsystemet (factoradic) och det decimala talsystemet.

### Funktionalitet
- Konverterar faktoriella tal till decimala tal
- Konverterar decimala tal till faktoriella tal
- Hanterar mycket stora tal med BigInteger
- Använder tecken 0-9 och A-Z för att representera siffror 0-35

### Det faktoriella talsystemet
Ett positionellt talsystem där varje position representerar en koefficient för en faktorial:
```
d_n × n! + d_(n-1) × (n-1)! + ... + d_2 × 2! + d_1 × 1!
```

### Algoritmer

#### Factoradic till Decimal
1. Läs siffror från höger till vänster
2. För position i, beräkna: siffervärde × (i+1)!
3. Summera alla bidrag

#### Decimal till Factoradic
1. Dividera med 2, resten = koefficient för 1!
2. Dividera kvoten med 3, resten = koefficient för 2!
3. Fortsätt tills kvoten blir 0
4. Vänd ordningen på koefficienterna

### Teckenrepresentation
- Siffror 0-9: Värden 0-9
- Bokstäver A-Z: Värden 10-35

### Datastrukturer
- `BigInteger`: Hanterar mycket stora tal
- `ArrayList<String>`: Samlar faktoriella siffror
- `Collections.reverse()`: Vänder sifferordning

### Komplexitet
- Tidskomplexitet: O(log n) för båda konverteringsriktningar
- Rymdkomplexitet: O(log n) för att lagra siffror

### Exempel
```
Factoradic "321" = 3×3! + 2×2! + 1×1! = 18 + 4 + 1 = 23 (decimal)
Decimal 23 → "321" (factoradic)
```

## Gemensamma programmeringstekniker

### Felhantering
- Validering av indata format
- Hantering av tomma eller ogiltiga värden
- Användning av try-with-resources för resurshantering

### Prestanda
- Effektiva datastrukturer (HashMap, StringBuilder)
- Optimerade algoritmer (√n-gräns för divisorer)
- BigInteger för stora talberäkningar

### Kodkvalitet
- Omfattande svenska kommentarer
- Tydlig variabelnamning
- Modulär design med separata metoder
- Konsekvent kodstil

## Testning och validering
Alla program bör testas med:
- Normala testfall
- Gränsfall (tomma indata, extremvärden)
- Felaktiga indata format
- Stora datamängder för prestandavalidering
