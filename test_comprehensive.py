#!/usr/bin/env python3

import subprocess
import sys
import os


def test_largest_island():
    print("Testing LargestIsland...")

    # Change to P5-LargestIsland directory
    original_dir = os.getcwd()
    os.chdir('P5-LargestIsland')

    try:
        # Read main test cases from file
        with open('largest_island_test.txt', 'r') as f:
            content = f.read().strip()

        # Parse test cases (skip comment lines and empty lines)
        lines = [line for line in content.split(
            '\n') if line.strip() and not line.startswith('#')]

        # Test case 1: First test case from file
        test_input1 = '\n'.join(lines[:4])  # 3 4, @.@., .@@., @...

        result1 = subprocess.run(['java', 'LargestIsland'],
                                 input=test_input1, text=True, capture_output=True)

        if result1.returncode != 0:
            print(
                f"ERROR: LargestIsland test 1 failed with return code {result1.returncode}")
            print(f"stderr: {result1.stderr}")
            return False

        expected1 = "3"
        if result1.stdout.strip() != expected1:
            print(
                f"ERROR: Test 1 - Expected {expected1}, got {result1.stdout.strip()}")
            return False

        # Test case 2: Second test case from file
        test_input2 = '\n'.join(lines[4:7])  # 2 2, @@, @@

        result2 = subprocess.run(['java', 'LargestIsland'],
                                 input=test_input2, text=True, capture_output=True)

        if result2.returncode != 0:
            print(
                f"ERROR: LargestIsland test 2 failed with return code {result2.returncode}")
            return False

        expected2 = "4"
        if result2.stdout.strip() != expected2:
            print(
                f"ERROR: Test 2 - Expected {expected2}, got {result2.stdout.strip()}")
            return False

        print("✅ LargestIsland main tests passed!")
        return True

    finally:
        os.chdir(original_dir)


def test_snow_depth():
    print("Testing SnowDepth...")

    # Change to P6-SnowDepth directory
    original_dir = os.getcwd()
    os.chdir('P6-SnowDepth')

    try:
        # Test case 1: Kattis sample test (from images)
        with open('kattis_sample_test.txt', 'r') as f:
            test_input = f.read()

        result = subprocess.run(['java', 'SnowDepth'],
                                input=test_input, text=True, capture_output=True)

        if result.returncode != 0:
            print(
                f"ERROR: SnowDepth Kattis sample failed with return code {result.returncode}")
            print(f"stderr: {result.stderr}")
            return False

        lines = result.stdout.strip().split('\n')
        expected_output = [
            "1903", "Luleå 2.8",
            "1907", "Nuuq 1.4",
            "1973", "Nagano 3.0",
            "2023", "Aspen 0.3", "Boden 0.3", "Chamonix 0.3", "Kalmar 0.3", "Kiruna 0.3",
            "2024", "Nagano 2.4", "Aspen 2.3", "Vallois 2.2", "Bergen 1.7"
        ]

        if lines != expected_output:
            print(f"ERROR: Kattis sample test failed")
            print(f"Expected: {expected_output}")
            print(f"Got: {lines}")
            return False

        # Test case 2: Multiple measurements same location
        test_input2 = """20211201 Uppsala 2.5
20211215 Uppsala 2.7
20211230 Uppsala 2.3"""

        result2 = subprocess.run(['java', 'SnowDepth'],
                                 input=test_input2, text=True, capture_output=True)

        if result2.returncode != 0:
            print(
                f"ERROR: SnowDepth multiple measurements failed with return code {result2.returncode}")
            print(f"stderr: {result2.stderr}")
            return False

        lines2 = result2.stdout.strip().split('\n')
        if lines2 != ["2021", "Uppsala 2.7"]:
            print(f"ERROR: Multiple measurements test failed")
            print(f"Expected: ['2021', 'Uppsala 2.7']")
            print(f"Got: {lines2}")
            return False

        # Test case 3: Error handling
        test_input3 = """20201201 Stockholm 0.5
invalid line
20201202 Göteborg 1.2"""

        result3 = subprocess.run(['java', 'SnowDepth'],
                                 input=test_input3, text=True, capture_output=True)

        if result3.returncode != 0:
            print(
                f"ERROR: SnowDepth error handling failed with return code {result3.returncode}")
            print(f"stderr: {result3.stderr}")
            return False

        print("✅ SnowDepth main tests passed!")
        return True

    finally:
        os.chdir(original_dir)


def main():
    print("Running comprehensive tests...")

    # Compile Problem 5
    print("Compiling Problem 5...")
    compile_result1 = subprocess.run(['javac', 'P5-LargestIsland/LargestIsland.java'],
                                     capture_output=True)

    if compile_result1.returncode != 0:
        print(f"ERROR: Problem 5 compilation failed")
        print(f"stderr: {compile_result1.stderr.decode()}")
        return False

    # Compile Problem 6
    print("Compiling Problem 6...")
    compile_result2 = subprocess.run(['javac', 'P6-SnowDepth/SnowDepth.java', 'P6-SnowDepth/SnowDepthData.java'],
                                     capture_output=True)

    if compile_result2.returncode != 0:
        print(f"ERROR: Problem 6 compilation failed")
        print(f"stderr: {compile_result2.stderr.decode()}")
        return False

    success = True
    success &= test_largest_island()
    success &= test_snow_depth()

    if success:
        print("\n🎉 All comprehensive tests passed!")
        return True
    else:
        print("\n❌ Some comprehensive tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
