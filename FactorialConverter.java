import java.math.BigInteger;

/**
 * FactorialConverter - Konverterar mellan faktoriella talsystem och decimala tal
 *
 * Detta program implementerar konvertering mellan det faktoriella talsystemet
 * (factorial number system) och det decimala talsystemet. Det faktoriella
 * talsystemet använder faktorialer som bas istället för potenser av 10.
 *
 * I det faktoriella talsystemet representeras ett tal som:
 * d_n × n! + d_(n-1) × (n-1)! + ... + d_2 × 2! + d_1 × 1!
 *
 * Exempel: "321" i faktoriellt system = 3×3! + 2×2! + 1×1! = 18 + 4 + 1 = 23
 *
 * Programmet använder BigInteger för att hantera stora tal som kan uppstå
 * vid beräkningar med faktorialer.
 */
public class FactorialConverter {

    /**
     * Konverterar ett tal från faktoriellt talsystem till decimalt
     *
     * Algoritm:
     * 1. Vänd strängen för att börja från minst signifikanta siffran
     * 2. <PERSON><PERSON><PERSON> varje position, beräkna dess bidrag: siffra × position!
     * 3. Summera alla bidrag för att få det decimala resultatet
     *
     * @param factorialNumber Strängen som representerar talet i faktoriellt system
     * @return BigInteger som representerar talet i decimalt system
     */
    public static BigInteger convertFactorialToDecimal(String factorialNumber) {
        // Specialfall: om inmatningen är "0", returnera 0
        if (factorialNumber.equals("0")) {
            return BigInteger.ZERO;
        }

        // Vänd strängen för att börja från minst signifikanta siffran (högra sidan)
        String reversed = new StringBuilder(factorialNumber).reverse().toString();
        BigInteger result = BigInteger.ZERO;
        BigInteger currentFactorial = BigInteger.ONE; // Representerar 0! = 1

        // Iterera genom varje position i det vända talet
        for (int position = 0; position < reversed.length(); position++) {
            char currentChar = reversed.charAt(position);
            int digitValue = convertCharToValue(currentChar);

            // Beräkna faktorialen för den aktuella positionen: (position + 1)!
            BigInteger positionFactorial = currentFactorial.multiply(BigInteger.valueOf(position + 1));

            // Uppdatera resultatet med den aktuella siffrans bidrag
            result = result.add(BigInteger.valueOf(digitValue).multiply(positionFactorial));

            // Spara faktorialen för nästa iteration
            currentFactorial = positionFactorial;
        }

        return result;
    }

    /**
     * Konverterar ett decimalt tal till faktoriellt talsystem
     *
     * Algoritm:
     * 1. Hitta den största faktorialen som är mindre än eller lika med talet
     * 2. Generera alla nödvändiga faktorialer upp till denna punkt
     * 3. För varje faktorial (från störst till minst), beräkna kvoten och resten
     * 4. Kvoten blir siffran på den positionen, resten används för nästa iteration
     *
     * @param decimalNumber BigInteger som representerar talet i decimalt system
     * @return Sträng som representerar talet i faktoriellt system
     */
    public static String convertDecimalToFactorial(BigInteger decimalNumber) {
        // Specialfall: om inmatningen är 0, returnera "0"
        if (decimalNumber.equals(BigInteger.ZERO)) {
            return "0";
        }

        // Hitta den största faktorialindex som behövs för konverteringen
        int maxFactorialIndex = findMaxFactorialIndex(decimalNumber);

        // Generera alla nödvändiga faktorialer i en array för effektiv åtkomst
        BigInteger[] factorials = new BigInteger[maxFactorialIndex];
        factorials[0] = BigInteger.ONE; // 1! = 1
        for (int i = 1; i < maxFactorialIndex; i++) {
            // Beräkna (i+1)! = i! × (i+1)
            factorials[i] = factorials[i - 1].multiply(BigInteger.valueOf(i + 1));
        }

        // Bygg den faktoriella talrepresentationen
        StringBuilder factorialRepresentation = new StringBuilder();
        BigInteger remainder = decimalNumber;

        // Iterera från den största faktorialen till den minsta
        for (int i = maxFactorialIndex - 1; i >= 0; i--) {
            BigInteger currentFactorial = factorials[i];
            // Dela talet med den aktuella faktorialen för att få kvot och rest
            BigInteger[] divisionResult = remainder.divideAndRemainder(currentFactorial);
            int quotient = divisionResult[0].intValue(); // Kvoten blir siffran
            remainder = divisionResult[1]; // Resten används för nästa iteration

            // Konvertera kvoten till motsvarande tecken och lägg till i resultatet
            factorialRepresentation.append(convertValueToChar(quotient));
        }

        return factorialRepresentation.toString();
    }

    /**
     * Hjälpmetod för att konvertera ett tecken till dess numeriska värde
     *
     * Hanterar både siffror (0-9) och bokstäver (A-Z) för att representera
     * värden från 0 till 35. Detta möjliggör representation av större siffror
     * i det faktoriella talsystemet.
     *
     * @param c Tecknet som ska konverteras
     * @return Det numeriska värdet av tecknet
     */
    private static int convertCharToValue(char c) {
        if (Character.isDigit(c)) {
            // Konvertera siffertecken '0'-'9' till värden 0-9
            return c - '0';
        } else {
            // Konvertera bokstavstecken 'A'-'Z' till värden 10-35
            return 10 + (c - 'A');
        }
    }

    /**
     * Hjälpmetod för att konvertera ett numeriskt värde till dess teckenrepresentation
     *
     * Konverterar värden 0-9 till siffertecken och värden 10-35 till bokstäver A-Z.
     * Detta möjliggör representation av större siffror i det faktoriella talsystemet.
     *
     * @param value Det numeriska värdet som ska konverteras
     * @return Tecknet som representerar värdet
     */
    private static char convertValueToChar(int value) {
        if (value < 10) {
            // Konvertera värden 0-9 till siffertecken '0'-'9'
            return (char) ('0' + value);
        } else {
            // Konvertera värden 10-35 till bokstavstecken 'A'-'Z'
            return (char) ('A' + (value - 10));
        }
    }

    /**
     * Hjälpmetod för att hitta det största faktorialindex som behövs för konvertering
     *
     * Denna metod bestämmer hur många faktorialer som behövs för att representera
     * det givna decimala talet i faktoriellt talsystem. Den hittar det största n
     * sådant att n! ≤ target < (n+1)!
     *
     * @param target Det decimala tal som ska konverteras
     * @return Det största faktorialindex som behövs
     */
    private static int findMaxFactorialIndex(BigInteger target) {
        BigInteger factorial = BigInteger.ONE; // Börja med 1! = 1
        int index = 1;

        // Fortsätt tills nästa faktorial blir större än måltalet
        while (true) {
            BigInteger nextFactorial = factorial.multiply(BigInteger.valueOf(index + 1));
            if (nextFactorial.compareTo(target) > 0) {
                break; // Nästa faktorial är för stor, avbryt
            }
            factorial = nextFactorial;
            index++;
        }

        return index;
    }

    /**
     * Huvudmetod som demonstrerar konvertering mellan faktoriellt och decimalt talsystem
     *
     * Programmet tar två kommandoradsargument:
     * 1. Ett tal i faktoriellt talsystem (sträng)
     * 2. Ett tal i decimalt talsystem (BigInteger)
     *
     * Utdata:
     * - Första raden: Det faktoriella talet konverterat till decimalt
     * - Andra raden: Det decimala talet konverterat till faktoriellt
     */
    public static void main(String[] args) {
        // Läs inmatningsvärden från kommandoradsargument
        String factorialInput = args[0];
        BigInteger decimalInput = new BigInteger(args[1]);

        // Utför konverteringar i båda riktningarna
        BigInteger decimalResult = convertFactorialToDecimal(factorialInput);
        String factorialResult = convertDecimalToFactorial(decimalInput);

        // Skriv ut resultaten
        System.out.println(decimalResult);
        System.out.println(factorialResult);
    }
}