import java.math.BigInteger; // Importerar BigInteger för att hantera mycket stora heltal, då fakultetstal kan bli väldigt stora.
import java.util.ArrayList; // Importerar ArrayList för att kunna skapa dynamiska listor.
import java.util.Collections; // Importerar Collections för att kunna sortera och manipulera listor.
import java.util.List; // Importerar List-gränssnittet.
import java.util.Scanner; // Importerar Scanner för att kunna läsa in data från användaren (eller en fil).

// Detta är huvudklassen för vårt program som hanterar konvertering mellan decimaltal och fakultetstal.
public class Factoradic {

    // Denna metod konverterar en sträng som representerar ett fakultetstal till dess decimala motsvarighet.
    // factoradicStr: Strängen som representerar fakultetstalet (t.ex. "654320", "32A40244706404200").
    public static BigInteger factoradicToDecimal(String factoradicStr) {
        BigInteger decimal = BigInteger.ZERO; // Börjar med ett decimaltal på 0.
        BigInteger factorial = BigInteger.ONE; // Börjar med 1! (1) för den första positionen.

        // Loopar igenom fakultetstalets siffror från höger till vänster (från minst signifikanta till mest signifikanta).
        // Detta motsvarar i! där i går från 1 och uppåt.
        for (int i = 0; i < factoradicStr.length(); i++) {
            // Hämtar den aktuella siffran från höger.
            char charDigit = factoradicStr.charAt(factoradicStr.length() - 1 - i);
            int digit; // Variabel för att lagra siffervärdet.

            // Konverterar tecknet till ett siffervärde.
            // Om tecknet är en siffra (0-9), konvertera direkt.
            if (Character.isDigit(charDigit)) {
                digit = Character.getNumericValue(charDigit);
            } else {
                // Om tecknet är en bokstav (A-Z), konvertera till motsvarande siffervärde (A=10, B=11, etc.).
                digit = charDigit - 'A' + 10;
            }

            // Lägger till (siffervärdet * i!) till det totala decimaltalet.
            // Vi använder BigInteger för att hantera stora tal.
            decimal = decimal.add(BigInteger.valueOf(digit).multiply(factorial));

            // Beräknar nästa fakultet: (i+1)! = i! * (i+1).
            // Eftersom i börjar på 0, representerar (i+1) den aktuella faktorn för fakulteten.
            // Nästa fakultet är (i+2)!, så vi multiplicerar med (i+2).
            factorial = factorial.multiply(BigInteger.valueOf(i + 2)); 
        }
        return decimal; // Returnerar det beräknade decimaltalet.
    }

    // Denna metod konverterar ett decimaltal till dess motsvarande fakultetstal som en sträng.
    // decimal: Decimaltalet som ska konverteras.
    public static String decimalToFactoradic(BigInteger decimal) {
        if (decimal.equals(BigInteger.ZERO)) { // Om decimaltalet är 0, är fakultetstalet också "0".
            return "0";
        }

        List<String> factoradicDigits = new ArrayList<>(); // Lista för att lagra fakultetssiffrorna.
        BigInteger i = BigInteger.valueOf(2); // Börjar med 2 för den första divisionen (för att få f_1, som är koefficienten för 1!).
                                             // Enligt algoritmen dividerar vi med 2 för att få f_1, sedan med 3 för f_2, osv.

        // Loopar så länge decimaltalet är större än 0.
        while (decimal.compareTo(BigInteger.ZERO) > 0) {
            // Beräknar resten när decimaltalet divideras med i.
            // Detta ger oss den aktuella fakultetssiffran (f_k).
            BigInteger remainder = decimal.remainder(i);
            
            // Konverterar siffervärdet till ett tecken (0-9 eller A-Z).
            String digitChar; 
            if (remainder.intValue() < 10) { // Om siffran är mindre än 10, konvertera till en vanlig siffra.
                digitChar = String.valueOf(remainder.intValue());
            } else { // Om siffran är 10 eller större, konvertera till en bokstav (A=10, B=11, etc.).
                digitChar = String.valueOf((char) ('A' + (remainder.intValue() - 10)));
            }
            factoradicDigits.add(digitChar); // Lägger till siffran i listan.

            // Dividerar decimaltalet med i för nästa iteration.
            decimal = decimal.divide(i);
            i = i.add(BigInteger.ONE); // Ökar i för att beräkna nästa divisor (i+1).
        }

        Collections.reverse(factoradicDigits); // Fakultetssiffrorna samlas i omvänd ordning, så vi måste vända på listan.
        return String.join("", factoradicDigits); // Slår ihop siffrorna till en enda sträng.
    }

    // Huvudmetoden där programmet startar och körs.
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in); // Skapar ett Scanner-objekt för att läsa in input.

        // Läser in det första talet som en sträng (fakultetstalet).
        String factoradicInput = scanner.nextLine();
        // Läser in det andra talet som en sträng och konverterar det till BigInteger (decimaltalet).
        BigInteger decimalInput = new BigInteger(scanner.nextLine());

        scanner.close(); // Stänger Scanner-objektet.

        // Konverterar fakultetstalet till decimal och skriver ut det.
        System.out.println(factoradicToDecimal(factoradicInput));
        // Konverterar decimaltalet till fakultetstal och skriver ut det.
        System.out.println(decimalToFactoradic(decimalInput));
    }
}


