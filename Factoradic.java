import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Scanner;

/**
 * Factoradic - Konvertering mellan faktoriella talsystem och decimala tal
 *
 * Detta program implementerar konvertering mellan det faktoriella talsystemet
 * (factoradic/factorial number system) och det decimala talsystemet.
 *
 * Det faktoriella talsystemet använder faktorialer som bas istället för potenser.
 * Ett tal representeras som: d_n × n! + d_(n-1) × (n-1)! + ... + d_2 × 2! + d_1 × 1!
 *
 * Exempel på konvertering:
 * - Factoradic "321" = 3×3! + 2×2! + 1×1! = 18 + 4 + 1 = 23 (decimal)
 * - Decimal 23 konverteras tillbaka till "321" (factoradic)
 *
 * Programmet använder BigInteger för att hantera mycket stora tal som kan
 * uppstå vid beräkningar med faktorialer, särskilt för stora index.
 *
 * Teckenrepresentation: 0-9 för värden 0-9, A-Z för värden 10-35
 */
public class Factoradic {

    /**
     * Konverterar en sträng som representerar ett faktoriellt tal till decimalt tal
     *
     * Algoritm:
     * 1. Läs siffrorna från höger till vänster (minst till mest signifikant)
     * 2. För varje position i, beräkna siffervärde × (i+1)!
     * 3. Summera alla bidrag för att få det decimala resultatet
     *
     * Matematisk grund: Varje position i representerar koefficienten för (i+1)!
     * Position 0 (längst till höger) = koefficient för 1!
     * Position 1 = koefficient för 2!, osv.
     *
     * @param factoradicStr Strängen som representerar det faktoriella talet
     *                      (t.ex. "654320", "32A40244706404200")
     * @return BigInteger som representerar det decimala värdet
     */
    public static BigInteger factoradicToDecimal(String factoradicStr) {
        BigInteger decimal = BigInteger.ZERO; // Ackumulator för det decimala resultatet
        BigInteger factorial = BigInteger.ONE; // Börjar med 1! = 1

        // Iterera genom siffrorna från höger till vänster
        for (int i = 0; i < factoradicStr.length(); i++) {
            // Hämta siffran från position (längd - 1 - i) för att läsa bakifrån
            char charDigit = factoradicStr.charAt(factoradicStr.length() - 1 - i);
            int digit;

            // Konvertera tecken till numeriskt värde
            if (Character.isDigit(charDigit)) {
                // Siffror 0-9 konverteras direkt
                digit = Character.getNumericValue(charDigit);
            } else {
                // Bokstäver A-Z konverteras till värden 10-35
                digit = charDigit - 'A' + 10;
            }

            // Lägg till detta positionens bidrag: siffervärde × aktuell fakultet
            decimal = decimal.add(BigInteger.valueOf(digit).multiply(factorial));

            // Beräkna nästa fakultet för nästa iteration
            // Från position i går vi till position i+1, så fakulteten blir (i+2)!
            factorial = factorial.multiply(BigInteger.valueOf(i + 2));
        }

        return decimal;
    }

    /**
     * Konverterar ett decimalt tal till dess motsvarande faktoriella representation
     *
     * Algoritm (baserad på successiv division):
     * 1. Dividera decimaltalet med 2, resten blir koefficienten för 1!
     * 2. Dividera kvoten med 3, resten blir koefficienten för 2!
     * 3. Fortsätt med 4, 5, 6... tills kvoten blir 0
     * 4. Vänd ordningen på koefficienterna för att få det faktoriella talet
     *
     * Matematisk grund: Varje division med (i+1) ger koefficienten för i!
     * Detta bygger på att n! = (n-1)! × n, så division med n ger kvot och rest
     *
     * @param decimal BigInteger som representerar det decimala talet
     * @return Sträng som representerar det faktoriella talet
     */
    public static String decimalToFactoradic(BigInteger decimal) {
        // Specialfall: noll representeras som "0" i båda talsystemen
        if (decimal.equals(BigInteger.ZERO)) {
            return "0";
        }

        // Lista för att samla fakultetssiffrorna (kommer att vändas senare)
        List<String> factoradicDigits = new ArrayList<>();
        // Börja med divisor 2 för att få koefficienten för 1!
        BigInteger i = BigInteger.valueOf(2);

        // Fortsätt divisionen tills decimaltalet blir 0
        while (decimal.compareTo(BigInteger.ZERO) > 0) {
            // Beräkna resten vid division med i - detta blir koefficienten
            BigInteger remainder = decimal.remainder(i);

            // Konvertera koefficienten till lämplig teckenrepresentation
            String digitChar;
            if (remainder.intValue() < 10) {
                // Värden 0-9 representeras som siffror
                digitChar = String.valueOf(remainder.intValue());
            } else {
                // Värden 10-35 representeras som bokstäver A-Z
                digitChar = String.valueOf((char) ('A' + (remainder.intValue() - 10)));
            }
            factoradicDigits.add(digitChar);

            // Förbered för nästa iteration
            decimal = decimal.divide(i); // Kvoten blir det nya talet att bearbeta
            i = i.add(BigInteger.ONE); // Nästa divisor (i+1)
        }

        // Vänd ordningen eftersom vi samlade siffrorna i omvänd ordning
        Collections.reverse(factoradicDigits);
        // Slå ihop alla siffror till en enda sträng
        return String.join("", factoradicDigits);
    }

    /**
     * Huvudmetod som demonstrerar konvertering mellan faktoriella och decimala talsystem
     *
     * Programmet läser två rader från standard input:
     * 1. Ett tal i faktoriellt talsystem (sträng med siffror 0-9 och bokstäver A-Z)
     * 2. Ett tal i decimalt talsystem (heltal som kan vara mycket stort)
     *
     * Utdata:
     * - Första raden: Det faktoriella talet konverterat till decimalt
     * - Andra raden: Det decimala talet konverterat till faktoriellt
     *
     * Exempel på körning:
     * Input:  "321"
     *         "23"
     * Output: "23"
     *         "321"
     */
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        // Läs det faktoriella talet som en sträng
        String factoradicInput = scanner.nextLine();
        // Läs det decimala talet och konvertera till BigInteger för stora tal
        BigInteger decimalInput = new BigInteger(scanner.nextLine());

        scanner.close();

        // Utför konverteringar och skriv ut resultaten
        System.out.println(factoradicToDecimal(factoradicInput));
        System.out.println(decimalToFactoradic(decimalInput));
    }
}


