import java.util.Scanner;

/**
 * RectanglePerimeter - Beräknar minsta möjliga omkrets för en rektangel
 *
 * Detta program tar en given area A och beräknar den minsta möjliga omkretsen
 * för en rektangel med heltalssidor som har denna area. Algoritmen itererar
 * genom alla möjliga divisorer av arean för att hitta det sidpar som ger
 * den minsta omkretsen.
 *
 * Matematisk grund: För en given area A = längd × bredd, är omkretsen
 * P = 2 × (längd + bredd). För att minimera P måste längd och bredd vara
 * så nära varandra som möjligt (närmare kvadratformen).
 */
public class RectanglePerimeter {
    /**
     * Huvudmetod som beräknar minsta omkrets för en rektangel med given area
     * där sidorna är heltalslängder.
     *
     * Algoritm:
     * 1. Iterera genom alla möjliga längder från 1 till √area
     * 2. <PERSON><PERSON>r varje längd som är en divisor av area, ber<PERSON>kna motsvarande bredd
     * 3. Beräkna omkretsen för detta sidpar
     * 4. Håll reda på den minsta omkretsen som hittats
     */
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);
        long area = scanner.nextLong();
        // Initialisera minsta omkrets till största möjliga värde
        long minimumPerimeter = Long.MAX_VALUE;

        // Iterera genom möjliga sidlängder upp till kvadratroten av arean
        // Detta är effektivt eftersom vi bara behöver kontrollera hälften av divisorerna
        for (long length = 1; length * length <= area; length++) {
            // Kontrollera om längden är en divisor av arean
            if (area % length == 0) {
                // Beräkna motsvarande bredd
                long width = area / length;
                // Beräkna omkretsen för detta rektangelpar
                long perimeter = 2 * (length + width);
                // Uppdatera minsta omkrets om denna är mindre
                minimumPerimeter = Math.min(minimumPerimeter, perimeter);
            }
        }
        // Skriv ut den minsta omkretsen som hittades
        System.out.println(minimumPerimeter);
        scanner.close();
    }
}