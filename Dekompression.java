import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Scanner;

/**
 * Dekompression - Program för att dekomprimera textserier
 *
 * Detta program läser komprimerad textdata i formatet "nyckel:värden;nyckel:värden"
 * och parsar den till en HashMap där varje nyckel mappas till en lista av heltal.
 * Programmet är designat för att hantera UTF-8-kodning för internationella tecken.
 *
 * Inmatningsformat: "text1:tal1 tal2 tal3;text2:tal4 tal5;..."
 * Exempel: "A:1 2 3;B:4 5;C:6"
 *
 * Observera: Detta program är under utveckling och dekompressionslogiken
 * är inte implementerad än.
 */
public class Dekompression {
    public static void main(String[] args) {
        // HashMap för att lagra textserier där nyckeln är text och värdet är en lista av heltal
        HashMap<String, List<Integer>> TextSeries = new HashMap<>();
        // Scanner med UTF-8-kodning för att hantera internationella tecken
        Scanner input = new Scanner(System.in, StandardCharsets.UTF_8);

        // Läs hela inmatningsraden
        String text = input.nextLine();

        // Dela upp inmatningen i delar separerade med semikolon
        String[] parts = text.split(";");

        // Iterera genom varje del av textserien
        for (String part : parts) {
            part = part.trim(); // Ta bort ledande och avslutande mellanslag

            if (!part.isEmpty()) {
                // Dela upp nyckel-värde-paret vid kolon
                String[] keyValue = part.split(":");

                // Säkerställ att nyckel-värde-paret har exakt två delar
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim(); // Extrahera och trimma nyckeln

                    // Dela upp värdesträngarna vid mellanslag och trimma
                    String[] values = keyValue[1].trim().split("\\s+");

                    // Skapa en lista för att lagra heltalsvärdena
                    List<Integer> intValues = new ArrayList<>();

                    // Konvertera varje värdesträng till heltal och lägg till i listan
                    for (String value : values) {
                        intValues.add(Integer.parseInt(value));
                    }

                    // Lägg till nyckel-värde-paret i HashMap
                    TextSeries.put(key, intValues);
                }
            }
        }

        // TODO: Implementera dekompressionslogiken här
        // Denna del av programmet är under utveckling

        // Debugging-utskrift av HashMap (kan tas bort senare)
        System.out.println("TextSeries: " + TextSeries);

        // TODO: Dekomprimera textserien och skriv ut resultatet

        input.close();
    }
}