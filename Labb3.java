
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * Labb3 - Textrekonstruktion från positionsdata
 *
 * Detta program rekonstruerar en ursprunglig textmening från komprimerad data
 * där varje ord är kopplat till sina positioner i den ursprungliga texten.
 *
 * Inmatningsformat: "ord1:pos1 pos2 pos3;ord2:pos4 pos5;..."
 * Exempel: "Hej:0;världen:4" -> "Hej världen"
 *
 * Programmet använder Java Streams för effektiv databehandling och StringBuilder
 * för dynamisk konstruktion av resultatsträngen. UTF-8-kodning används för att
 * hantera internationella tecken korrekt.
 *
 * Algoritm:
 * 1. Parsa inmatningen till en Map med ord som nycklar och positionslistor som värden
 * 2. Bygg resultatsträngen genom att placera varje ord på dess angivna positioner
 * 3. Använd StringBuilder för effektiv strängmanipulation
 */
public class Labb3 {
    /**
     * Huvudmetod som bearbetar inmatningstext och rekonstruerar ett meddelande
     *
     * Metoden läser indata i formatet "ord:positioner;" där positioner är
     * mellanslags-separerade heltal, och rekonstruerar det ursprungliga
     * meddelandet genom att placera varje ord på dess specificerade positioner.
     *
     * Använder funktionell programmering med Java Streams för elegant
     * databehandling och LinkedHashMap för att bevara ordningen på inmatningen.
     */
    public static void main(String[] args) {
        // LinkedHashMap för att bevara ordningen på inmatade ord
        Map<String, List<Integer>> TextSeries = new LinkedHashMap<>();
        // Scanner med UTF-8-kodning för korrekt hantering av internationella tecken
        Scanner input = new Scanner(System.in, StandardCharsets.UTF_8);
        String text = input.nextLine();

        // Bearbeta inmatningstexten med funktionell programmering (Java Streams)
        Arrays.stream(text.split(";")) // Dela upp texten vid semikolon för att få ord:position-par
              .map(String::trim) // Ta bort ledande och avslutande mellanslag från varje del
              .filter(part -> !part.isEmpty()) // Filtrera bort tomma delar
              .forEach(part -> { // Bearbeta varje del (ord:positioner)
                  String[] keyValue = part.split(":"); // Dela upp nyckel-värde-paret vid kolon
                  if (keyValue.length == 2) { // Säkerställ att paret har exakt två delar
                      String key = keyValue[0].trim(); // Extrahera ordet (nyckeln)
                      // Konvertera positionssträngarna till en lista av heltal
                      List<Integer> values = Arrays.stream(keyValue[1].trim().split("\\s+"))
                                                   .map(Integer::parseInt) // Konvertera varje position till heltal
                                                   .collect(java.util.stream.Collectors.toList());
                      TextSeries.put(key, values); // Lägg till ord-position-paret i Map
                  }
              });

        // Använd StringBuilder för dynamisk konstruktion av resultatsträngen
        StringBuilder result = new StringBuilder();

        // Bearbeta varje ord och dess positioner för att bygga slutresultatet
        TextSeries.forEach((key, positions) -> {
            for (int pos : positions) { // Iterera genom alla positioner för det aktuella ordet
                // Säkerställ att StringBuilder är tillräckligt lång för att rymma ordet
                while (result.length() <= pos + key.length() - 1) {
                    result.append(' '); // Lägg till mellanslag för att utöka längden
                }

                // Infoga ordet på rätt position tecken för tecken
                for (int i = 0; i < key.length(); i++) {
                    // Sätt tecknet på den beräknade positionen
                    result.setCharAt(pos + i, key.charAt(i));
                }
            }
        });

        // Skriv ut resultatet och ta bort eventuella avslutande mellanslag
        System.out.println(result.toString().stripTrailing());

        input.close();
    }
}