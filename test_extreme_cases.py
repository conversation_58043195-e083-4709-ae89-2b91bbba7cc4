#!/usr/bin/env python3

import subprocess
import sys
import os


def test_largest_island_extreme():
    print("Testing LargestIsland with extreme cases...")

    # Change to P5-LargestIsland directory
    original_dir = os.getcwd()
    os.chdir('P5-LargestIsland')

    try:
        # Test case 1: No islands
        test_input = """3 3
...
...
..."""

        result = subprocess.run(['java', 'LargestIsland'],
                                input=test_input, text=True, capture_output=True)

        if result.returncode != 0:
            print(f"ERROR: No islands test failed")
            return False

        if result.stdout.strip() != "0":
            print(
                f"ERROR: Expected 0 for no islands, got {result.stdout.strip()}")
            return False

        # Test case 2: All islands
        test_input2 = """2 3
@@@
@@@"""

        result2 = subprocess.run(['java', 'LargestIsland'],
                                 input=test_input2, text=True, capture_output=True)

        if result2.returncode != 0:
            print(f"ERROR: All islands test failed")
            return False

        if result2.stdout.strip() != "6":
            print(
                f"ERROR: Expected 6 for all islands, got {result2.stdout.strip()}")
            return False

        print("✅ LargestIsland extreme cases passed!")
        return True

    finally:
        os.chdir(original_dir)


def test_snow_depth_extreme():
    print("Testing SnowDepth with extreme cases...")

    # Change to P6-SnowDepth directory
    original_dir = os.getcwd()
    os.chdir('P6-SnowDepth')

    try:
        # Test case 1: Same depth, different locations
        test_input = """20201201 Stockholm 1.0
20201202 Göteborg 1.0
20201203 Malmö 1.0"""

        result = subprocess.run(['java', 'SnowDepth'],
                                input=test_input, text=True, capture_output=True)

        if result.returncode != 0:
            print(f"ERROR: Same depth test failed")
            return False

        lines = result.stdout.strip().split('\n')
        # Should be sorted alphabetically when depths are equal
        expected_order = ["Göteborg", "Malmö", "Stockholm"]
        for i, expected_city in enumerate(expected_order):
            if not lines[i+1].startswith(expected_city):
                print(
                    f"ERROR: Expected {expected_city} at position {i+1}, got {lines[i+1]}")
                return False

        # Test case 2: Multiple years
        test_input2 = """20201201 Stockholm 1.0
20211201 Stockholm 2.0
20221201 Stockholm 3.0"""

        result2 = subprocess.run(['java', 'SnowDepth'],
                                 input=test_input2, text=True, capture_output=True)

        if result2.returncode != 0:
            print(f"ERROR: Multiple years test failed")
            return False

        lines2 = result2.stdout.strip().split('\n')
        # Every other line should be a year
        years = [lines2[0], lines2[2], lines2[4]]
        if years != ["2020", "2021", "2022"]:
            print(f"ERROR: Expected years [2020, 2021, 2022], got {years}")
            return False

        # Test case 3: Top 5 selection with more than 5 locations
        test_input3 = """20201201 Location1 10.0
20201202 Location2 9.0
20201203 Location3 8.0
20201204 Location4 7.0
20201205 Location5 6.0
20201206 Location6 5.0
20201207 Location7 4.0"""

        result3 = subprocess.run(['java', 'SnowDepth'],
                                 input=test_input3, text=True, capture_output=True)

        if result3.returncode != 0:
            print(f"ERROR: Top 5 selection test failed")
            return False

        lines3 = result3.stdout.strip().split('\n')
        # Should only show top 5 locations
        expected3 = ["2020", "Location1 10.0", "Location2 9.0",
                     "Location3 8.0", "Location4 7.0", "Location5 6.0"]
        if lines3 != expected3:
            print(f"ERROR: Top 5 selection failed")
            print(f"Expected: {expected3}")
            print(f"Got: {lines3}")
            return False

        # Test case 4: Multi-word locations with special characters
        test_input4 = """20201201 New York City 1.5
20201202 Los Angeles County 1.3
20201203 San Francisco Bay Area 1.7
20201204 Åre 2.1"""

        result4 = subprocess.run(['java', 'SnowDepth'],
                                 input=test_input4, text=True, capture_output=True)

        if result4.returncode != 0:
            print(f"ERROR: Multi-word locations test failed")
            return False

        lines4 = result4.stdout.strip().split('\n')
        # Should handle multi-word locations correctly
        if "New York City" not in lines4[2] or "Los Angeles County" not in lines4[3]:
            print(f"ERROR: Multi-word locations not handled correctly")
            print(f"Got: {lines4}")
            return False

        print("✅ SnowDepth extreme cases passed!")
        return True

    finally:
        os.chdir(original_dir)


def main():
    print("Running extreme case tests...")

    # Compile Problem 5
    print("Compiling Problem 5...")
    compile_result1 = subprocess.run(['javac', 'P5-LargestIsland/LargestIsland.java'],
                                     capture_output=True)

    if compile_result1.returncode != 0:
        print("ERROR: Problem 5 compilation failed")
        print(f"stderr: {compile_result1.stderr.decode()}")
        return False

    # Compile Problem 6
    print("Compiling Problem 6...")
    compile_result2 = subprocess.run(['javac', 'P6-SnowDepth/SnowDepth.java', 'P6-SnowDepth/SnowDepthData.java'],
                                     capture_output=True)

    if compile_result2.returncode != 0:
        print("ERROR: Problem 6 compilation failed")
        print(f"stderr: {compile_result2.stderr.decode()}")
        return False

    success = True
    success &= test_largest_island_extreme()
    success &= test_snow_depth_extreme()

    if success:
        print("\n🎉 All extreme case tests passed!")
        return True
    else:
        print("\n❌ Some extreme case tests failed!")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
