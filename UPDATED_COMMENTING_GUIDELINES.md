# Refined Java Code Commenting Guidelines (Swedish)

## Core Principles

### Fokuserad Kommentarstil för Java
Denna kommentarstil fokuserar på **komplexitet och kärnlogik** snarare än att kommentera all kod. Målet är att hjälpa med förståelse av viktiga delar och förbereda för presentationer.

### What NOT to Comment (Simple Code)
- **Simple imports** - Standard library imports (t.ex. `import java.util.Scanner;`)
- **Basic object initialization** - Självförklarande initialisering (t.ex. `Scanner scanner = new Scanner(System.in);`)
- **Simple variable declarations** - Enkla deklarationer (t.ex. `int maxSize = 0;`)
- **Obvious operations** - Uppenbara operationer (t.ex. `scanner.close()`)
- **Standard getters/setters** - Enkla accessor-metoder

### What TO Comment (Complex & Core Logic)
- **Complex algorithms** - DFS, sortering, matematiska beräkningar
- **Core business logic** - Huvudlogiken för problemlösningen
- **Complex data structures** - Avancerade datastrukturer och deras användning
- **Non-obvious optimizations** - Minnesbesparande tekniker, prestandaförbättringar
- **Important design decisions** - Varför vissa lösningar valdes
- **Complex parsing/processing** - Avancerad databehandling

## Additional Guidelines

### Fokus på Komplexitet
- **Kommentarer ska främst riktas mot funktioner, klasser och kodblock som är komplexa eller utgör den centrala logiken för lösningen**
- **Syfte**: Målet är att kommentarerna ska hjälpa dig att förstå och förklara kodens viktiga delar och hur de bidrar till lösningen, särskilt inför presentationer

### Key Rules
1. **Only comment simple code if it's important and appears frequently** (keep explanations brief)
2. **Focus commenting efforts on complex functions/classes** that contain the core logic
3. **All comments in Swedish** unless another language is specified
4. **Explain "why" and "how"** for complex logic, not "what" for obvious code

## Examples

### ✅ Preferred Style (Focused on Complex Logic)
```java
import java.util.Scanner;
import java.util.Stack;

public class LargestIsland {
    private static final int[] dx = {-1, 1, 0, 0};
    private static final int[] dy = {0, 0, -1, 1};

    private static int rows;
    private static int cols;
    private static char[][] grid;
    private static boolean[][] visited;

    // Minnesoptimerad iterativ DFS som använder koordinatkodning för att undvika minnesläckage
    private static int iterativeDfs(int r, int c) {
        // Använder heltalskodning istället för int[]-arrayer för att spara minne
        Stack<Integer> stack = new Stack<>();
        stack.push(r * cols + c); // Kodar koordinater: encoded = rad * cols + kolumn
        visited[r][c] = true;
        int currentIslandSize = 0;

        while (!stack.isEmpty()) {
            int encoded = stack.pop();
            int currentR = encoded / cols; // Avkodar rad
            int currentC = encoded % cols; // Avkodar kolumn
            currentIslandSize++;

            // Utforska alla fyra riktningar för sammanhängande landceller
            for (int i = 0; i < 4; i++) {
                int newR = currentR + dx[i];
                int newC = currentC + dy[i];

                if (newR >= 0 && newR < rows && newC >= 0 && newC < cols &&
                    grid[newR][newC] == '@' && !visited[newR][newC]) {

                    visited[newR][newC] = true;
                    stack.push(newR * cols + newC);
                }
            }
        }
        return currentIslandSize;
    }
```

### ❌ Over-commented Simple Code (Avoid This)
```java
import java.util.Scanner; // Importerar Scanner för input
import java.util.Stack; // Importerar Stack för DFS

public class LargestIsland {
    private static int rows; // Antal rader
    private static int cols; // Antal kolumner

    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in); // Skapar scanner
        rows = scanner.nextInt(); // Läser rader
        cols = scanner.nextInt(); // Läser kolumner
```

## Implementation Rules

1. **Use Swedish for all comments** - Default language unless specifically requested otherwise
2. **Skip simple imports and basic operations** - Don't comment obvious standard library imports
3. **Focus on complex algorithms** - Prioritize DFS, parsing, optimization techniques
4. **Explain core business logic** - Comment the main problem-solving approach
5. **Document important design decisions** - Why certain data structures or approaches were chosen
6. **Comment frequent important patterns** - If simple code appears often and is crucial, add brief comments
7. **Prepare for presentations** - Comments should help explain the solution's key components

## Benefits

- **Focused learning** - Comments highlight the most important and complex parts
- **Presentation ready** - Easy to explain core logic and algorithms to others
- **Cleaner code** - Less visual clutter from obvious comments
- **Better maintenance** - Complex parts are well-documented where it matters most
- **Efficient studying** - Quickly identify and understand the critical components
