import java.util.HashMap;
import java.util.Scanner;

/**
 * LetterCounter - Räknar frekvensen av bokstäver i en textinmatning
 *
 * Detta program läser en textrad från användaren och räknar hur många gånger
 * varje bokstav (A-Z) förekommer i texten. Programmet ignorerar icke-alfabetiska
 * tecken och konverterar alla bokstäver till versaler för enhetlig räkning.
 *
 * Utdata: Visar varje bokstav som förekommer följt av dess frekvens i formatet
 * "BOKSTAV:ANTAL", separerat med mellanslag. Om inga bokstäver hittas skrivs "0".
 */
public class LetterCounter {
    public static void main(String[] args) {
        Scanner input = new Scanner(System.in);
        // HashMap för att lagra frekvensen av varje bokstav (A-Z)
        HashMap<Character, Integer> letterFrequency = new HashMap<>();

        // Initialisera HashMap med alla alfabetiska bokstäver (A-Z) satta till 0
        for (char ch = 'A'; ch <= 'Z'; ch++) {
            letterFrequency.put(ch, 0);
        }

        // Läs inmatningstext från användaren
        String text = input.nextLine();
        // Filtrera bort alla icke-alfabetiska tecken med regex
        text = text.replaceAll("[^a-zA-Z]", "");

        // Iterera genom varje tecken i den filtrerade texten
        for (char ch : text.toCharArray()) {
            // Konvertera till versal för enhetlig behandling
            char uppercaseChar = Character.toUpperCase(ch);
            // Öka räknaren för den aktuella bokstaven med 1
            letterFrequency.put(uppercaseChar, letterFrequency.get(uppercaseChar) + 1);
        }

        input.close();

        // Bygg resultatsträngen med bokstäver som har frekvens > 0
        StringBuilder result = new StringBuilder();
        for (char ch = 'A'; ch <= 'Z'; ch++) {
            int count = letterFrequency.get(ch);
            if (count > 0) {
                // Lägg till bokstav:antal följt av mellanslag
                result.append(ch).append(":").append(count).append(" ");
            }
        }

        // Skriv ut resultatet eller "0" om inga bokstäver räknades
        if (result.length() == 0) {
            System.out.println("0");
        } else {
            // Ta bort det sista mellanslaget innan utskrift
            System.out.println(result.toString().trim());
        }
    }
}
