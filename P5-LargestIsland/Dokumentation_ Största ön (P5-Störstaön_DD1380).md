# Dokumentation: <PERSON><PERSON>rst<PERSON> ön (P5-<PERSON><PERSON>rstaön_DD1380)

Denna dokumentation beskriver implementeringen av lösningen till problemet "Största ön". Målet är att du ska kunna förstå kodens funktionalitet och logik för att på ett övertygande sätt presentera den.

## Problembeskrivning

Problemet går ut på att räkna ut hur stor den största sammanhängande ön är på ett sjökort. Sjökortet representeras av ett rutnät med tecknen '∼' för vatten och '@' för land. Två '@'-tecken tillhör samma ö om de angränsar till varandra rakt i sidled eller höjdled (diagonaler räknas inte).

**Indata:** Indata inleds med två tal, `x` (antal rader) och `y` (antal kolumner). Därefter följer `x` rader med `y` tecken i varje, best<PERSON><PERSON>e av '∼' och '@'.

**Utdata:** <PERSON>tt heltal som anger storleken på den största ön. Om ingen ö finns, ska svaret vara '0'.

## Lösningens Översikt

Lösningen använder en algoritm som kallas Breadth-First Search (BFS) för att hitta och mäta storleken på öar i sjökortet. Huvudidén är att:

1.  Iterera igenom varje cell i sjökortet.
2.  Om en landcell ('@') hittas som inte redan har besökts (dvs. tillhör en ö som vi inte redan har räknat):
    *   Starta en BFS-sökning från denna cell.
    *   BFS-sökningen kommer att iterativt besöka alla angränsande landceller som tillhör samma ö och markera dem som besökta.
    *   Storleken på den aktuella ön (antalet '@'-tecken) räknas under BFS-sökningen.
3.  Hålla reda på den största östorleken som hittills har påträffats.

Klassen `LargestIsland` innehåller all logik för detta.

## Funktionsspecifikationer

### Klass: `LargestIsland`

Denna klass innehåller logiken för att läsa in sjökortet, hitta öar och bestämma storleken på den största.

*   **Metoder:**
    *   `private static int bfsIterative(char[][] grid, boolean[][] visited, int startR, int startC, int rows, int cols)`
        *   **Beskrivning:** Utför en iterativ Breadth-First Search (BFS) från en given startcell (`startR`, `startC`) för att hitta alla sammanhängande landceller som tillhör samma ö. Metoden använder en kö för att systematiskt utforska alla grannar.
        *   **Parametrar:**
            *   `grid`: 2D-array som representerar sjökortet med land (@) och vatten (.)
            *   `visited`: 2D-array som håller reda på vilka celler som redan besökts
            *   `startR`: Radindex för startcellen
            *   `startC`: Kolumnindex för startcellen
            *   `rows`: Totalt antal rader i sjökortet
            *   `cols`: Totalt antal kolumner i sjökortet
        *   **Returvärde:** Ett heltal som representerar storleken (antalet '@'-tecken) på den ö som hittades med start från (`startR`, `startC`)

    *   `public static void main(String[] args)`
        *   **Beskrivning:** Huvudmetoden där programmet startar. Den ansvarar för att:
            1.  Läsa in dimensionerna på sjökortet (rader och kolumner).
            2.  Läsa in själva sjökortet och lagra det i `grid`-arrayen.
            3.  Initiera `visited`-arrayen.
            4.  Iterera igenom varje cell i `grid`.
            5.  Om en obesökt landcell ('@') hittas, anropa `bfsIterative` för att räkna storleken på den ön.
            6.  Hålla reda på och uppdatera `maxSize`.
            7.  Skriva ut `maxSize`.

## Kodgenomgång (Workflow)

Lösningen följer ett systematiskt tillvägagångssätt för att hitta den största ön:

### 1. Inläsning och Initialisering (`main`-metoden)

*   **Dimensioner:** Programmet börjar med att läsa in antalet rader (`rows`) och kolumner (`cols`) från de två första talen i indata.
*   **Rutnät (Grid):** Sjökortet (`grid`) och `visited`-arrayen skapas med de inlästa dimensionerna.
*   **Sjökortsdata:** Därefter läses varje rad av sjökortet in som en sträng och konverteras till en teckenarray som lagras i `grid`.
*   **Maximal ö-storlek:** En variabel `maxIslandSize` initieras till 0. Denna kommer att hålla reda på storleken på den största ön som hittills har hittats.

### 2. Iteration och Sökning efter Öar (`main`-metoden)

Programmet använder nästlade loopar för att gå igenom varje cell (`i` för rad, `j` för kolumn) i `grid`:

*   **Villkor för BFS-start:** För varje cell kontrolleras två saker:
    1.  Är cellen land (`grid[i][j] == '@'`)?
    2.  Har cellen *inte* redan besökts (`!visited[i][j]`)?
*   **Starta BFS:** Om båda villkoren är sanna, betyder det att vi har hittat en ny, obesökt del av en ö. Då anropas `bfsIterative(grid, visited, i, j, rows, cols)`-metoden för att utforska och räkna storleken på just denna ö.
*   **Uppdatera maximal storlek:** Returvärdet från `bfsIterative` (storleken på den just analyserade ön) jämförs med `maxSize`. Om den nya ön är större, uppdateras `maxSize`.

### 3. Breadth-First Search (`bfsIterative`-metoden)

Denna iterativa metod är kärnan i ö-sökningen och använder en kö för att systematiskt utforska ön:

*   **Initialisering:** Metoden skapar en kö (`Queue<int[]>`) och lägger till startpositionen. Startcellen markeras omedelbart som besökt (`visited[startR][startC] = true`).
*   **Riktningsarray:** En 2D-array `directions` definierar de fyra möjliga rörelseriktningarna: upp (-1,0), ner (1,0), vänster (0,-1), höger (0,1).
*   **BFS-loop:** Medan kön inte är tom:
    1.  **Ta nästa cell:** Den första cellen tas från kön (`queue.poll()`)
    2.  **Räkna cell:** Storleksräknaren (`size`) ökas med 1
    3.  **Utforska grannar:** För varje av de fyra riktningarna kontrolleras granncellen:
        *   **Gränskontroll:** Ligger grannen inom sjökortets dimensioner?
        *   **Landkontroll:** Är grannen en landcell (`grid[newR][newC] == '@'`)?
        *   **Besökskontroll:** Har grannen inte redan besökts (`!visited[newR][newC]`)?
    4.  **Lägg till granne:** Om alla villkor är uppfyllda, markeras grannen som besökt och läggs till i kön för framtida utforskning
*   **Returnera ö-storlek:** När kön är tom har hela ön utforskats och den totala storleken returneras.

### 4. Utskrift (`main`-metoden)

När iterationen genom hela `grid` är klar, kommer `maxSize` att innehålla storleken på den största ön som hittades (eller 0 om inga öar fanns). Detta värde skrivs sedan ut.

Detta tillvägagångssätt garanterar att varje landcell besöks högst en gång och att alla sammanhängande delar av en ö korrekt identifieras och räknas. BFS-algoritmen är särskilt minneseffektiv för detta problem eftersom den undviker djupa rekursiva anrop som kan orsaka stack overflow för stora öar.

## Aktuell Status

**Testresultat:** Programmet klarar för närvarande 5 av 8 testfall på Kattis. Testfall 6 misslyckas med "Memory limit exceeded".

**Optimeringar som implementerats:**
- Iterativ BFS istället för rekursiv DFS för att undvika stack overflow
- Effektiv minneshantering med boolean-array för besökta celler
- Minimal objektskapelse under BFS-traversering

**Kända problem:**
- Testfall 6 överskrider minnesgränsen, troligen på grund av mycket stora rutnät eller komplexa östrukturer
- Ytterligare minnesoptimeringar kan behövas för att klara alla testfall

