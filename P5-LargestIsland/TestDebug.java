import java.util.*;

public class TestDebug {
    public static void main(String[] args) {
        Scanner sc = new Scanner(System.in);
        
        System.err.println("Reading input...");
        int rows = sc.nextInt();
        int cols = sc.nextInt();
        System.err.println("Rows: " + rows + ", Cols: " + cols);
        
        sc.nextLine(); // consume newline
        
        char[][] grid = new char[rows][cols];
        
        for (int i = 0; i < rows; i++) {
            String line = sc.nextLine();
            System.err.println("Line " + i + ": " + line);
            for (int j = 0; j < cols; j++) {
                grid[i][j] = j < line.length() ? line.charAt(j) : '.';
            }
        }
        
        System.err.println("Grid read successfully");
        System.out.println("1");
        sc.close();
    }
}
