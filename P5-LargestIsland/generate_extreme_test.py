#!/usr/bin/env python3

# Generate an extreme test case for LargestIsland to test memory limits
rows = 2000
cols = 2000

print(f"{rows} {cols}")

# Create a worst-case scenario: many small islands scattered throughout
# This will cause maximum memory usage in the DFS stack
for i in range(rows):
    row = ""
    for j in range(cols):
        # Create a checkerboard pattern of small islands
        # This maximizes the number of separate DFS calls
        if (i + j) % 4 == 0:
            row += "@"
        else:
            row += "."
    print(row)
