import java.util.*;

/**
 * LargestIsland - Lösning för att hitta den största ön i ett rutnät
 *
 * Denna klass implementerar en lösning för att hitta den största sammanhängande ön
 * i ett 2D-rutnät där '@' representerar land och '.' representerar vatten.
 * Använder BFS (Breadth-First Search) för att utforska varje ö och räkna dess storlek.
 */
public class LargestIsland {
    /**
     * Huvudmetod som läser indata, skapar rutnätet och hittar den största ön
     *
     * @param args kommandoradsargument (används ej)
     */
    public static void main(String[] args) {
        Scanner scanner = new Scanner(System.in);

        // Läs dimensioner för rutnätet
        int rows = scanner.nextInt();
        int cols = scanner.nextInt();
        scanner.nextLine(); // Konsumera newline efter siffrorna

        // Skapa och fyll rutnätet med indata
        char[][] grid = new char[rows][];
        for (int i = 0; i < rows; i++) {
            grid[i] = scanner.nextLine().toCharArray();
        }

        // Skapa besökt-array för att hålla reda på vilka celler som redan utforskats
        boolean[][] visited = new boolean[rows][cols];
        int maxSize = 0; // Håller reda på den största ön som hittats hittills

        // Gå igenom varje cell i rutnätet
        for (int i = 0; i < rows; i++) {
            for (int j = 0; j < cols; j++) {
                // Om vi hittar en obesökt landcell, starta BFS för att utforska hela ön
                if (grid[i][j] == '@' && !visited[i][j]) {
                    int size = bfsIterative(grid, visited, i, j, rows, cols);
                    maxSize = Math.max(maxSize, size);
                }
            }
        }

        System.out.println(maxSize);
        scanner.close();
    }

    /**
     * Iterativ BFS-implementation för att utforska en ö och räkna dess storlek
     *
     * Denna metod använder en kö för att systematiskt utforska alla sammanhängande
     * landceller (@) från en startposition. Den markerar besökta celler för att
     * undvika att räkna samma cell flera gånger.
     *
     * @param grid rutnätet med land (@) och vatten (.)
     * @param visited array som håller reda på vilka celler som redan besökts
     * @param startR startrad för utforskningen
     * @param startC startkolumn för utforskningen
     * @param rows totalt antal rader i rutnätet
     * @param cols totalt antal kolumner i rutnätet
     * @return storleken på ön (antal sammanhängande landceller)
     */
    private static int bfsIterative(char[][] grid, boolean[][] visited, int startR, int startC, int rows, int cols) {
        // Skapa kö för BFS och lägg till startpositionen
        Queue<int[]> queue = new LinkedList<>();
        queue.offer(new int[]{startR, startC});
        visited[startR][startC] = true;

        int size = 0; // Räknare för öns storlek

        // Riktningsarray för att kontrollera alla fyra grannar (upp, ner, vänster, höger)
        int[][] directions = {{-1, 0}, {1, 0}, {0, -1}, {0, 1}};

        // Fortsätt så länge det finns celler att utforska
        while (!queue.isEmpty()) {
            // Ta nästa cell från kön
            int[] current = queue.poll();
            int r = current[0];
            int c = current[1];
            size++; // Öka storleksräknaren

            // Kontrollera alla fyra grannar
            for (int[] dir : directions) {
                int newR = r + dir[0];
                int newC = c + dir[1];

                // Kontrollera om grannen är inom gränserna, obesökt och är land
                if (newR >= 0 && newR < rows && newC >= 0 && newC < cols &&
                    !visited[newR][newC] && grid[newR][newC] == '@') {
                    visited[newR][newC] = true; // Markera som besökt
                    queue.offer(new int[]{newR, newC}); // Lägg till i kön för utforskning
                }
            }
        }

        return size;
    }
}