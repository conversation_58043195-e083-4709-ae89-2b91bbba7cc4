#!/usr/bin/env python3

# Generate worst-case scenario: one huge connected component
# This will test the maximum stack depth in DFS
rows = 1500
cols = 1500

print(f"{rows} {cols}")

# Create one giant island that covers most of the grid
# This will create the deepest possible DFS stack
for i in range(rows):
    row = ""
    for j in range(cols):
        # Create a spiral pattern that forces maximum stack depth
        if i < rows - 1 and j < cols - 1:
            row += "@"
        else:
            row += "."
    print(row)
